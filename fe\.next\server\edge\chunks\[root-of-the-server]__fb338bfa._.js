(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__fb338bfa._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/app/api/chat/route.ts [app-edge-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST,
    "runtime": ()=>runtime
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-edge-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-edge-route] (ecmascript)");
;
const runtime = 'edge';
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$edge$2d$route$5d$__$28$ecmascript$29$__["OpenAI"]({
    apiKey: process.env.OPENAI_API_KEY
});
async function POST(req) {
    const { threadId, message } = await req.json();
    const assistantId = process.env.ASSISTANT_ID;
    if (!assistantId) {
        return new Response('Missing ASSISTANT_ID', {
            status: 500
        });
    }
    if (!message) {
        return new Response('Missing message', {
            status: 400
        });
    }
    // 1. Buat thread jika belum ada
    let thread_id = threadId;
    let newThread = null;
    if (!thread_id) {
        newThread = await openai.beta.threads.create();
        thread_id = newThread.id;
    }
    // 2. Tambahkan pesan user ke thread
    await openai.beta.threads.messages.create(thread_id, {
        role: 'user',
        content: message
    });
    // 3. Jalankan assistant (run)
    const run = await openai.beta.threads.runs.create(thread_id, {
        assistant_id: assistantId,
        stream: true
    });
    // 4. Stream event ke client
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
        async start (controller) {
            // Kirim event thread.created jika thread baru dibuat
            if (newThread) {
                controller.enqueue(encoder.encode(JSON.stringify({
                    event: 'thread.created',
                    data: {
                        id: newThread.id
                    }
                }) + '\n'));
            }
            for await (const event of run){
                controller.enqueue(encoder.encode(JSON.stringify(event) + '\n'));
                if (event.event === 'thread.run.completed') break;
            }
            controller.close();
        }
    });
    return new Response(stream, {
        headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache'
        }
    });
}
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__fb338bfa._.js.map