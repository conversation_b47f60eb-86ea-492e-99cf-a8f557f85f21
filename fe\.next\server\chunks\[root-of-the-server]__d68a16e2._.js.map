{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\nexport async function POST(req: NextRequest) {\r\n  try {\r\n    const { threadId, message } = await req.json();\r\n    \r\n    if (!message) {\r\n      return new Response('Missing message', { status: 400 });\r\n    }\r\n\r\n    // Proxy request to backend\r\n    const response = await fetch(`${API_URL}/api/chat`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        threadId,\r\n        message,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const error = await response.json();\r\n      return new Response(JSON.stringify(error), { \r\n        status: response.status,\r\n        headers: { 'Content-Type': 'application/json' }\r\n      });\r\n    }\r\n\r\n    const data = await response.json();\r\n    return new Response(JSON.stringify(data), {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Frontend API error:', error);\r\n    return new Response(JSON.stringify({ \r\n      error: 'Internal server error',\r\n      message: error.message \r\n    }), { \r\n      status: 500,\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n} "], "names": [], "mappings": ";;;AAEA,MAAM,UAAU,6DAAmC;AAE5C,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,IAAI;QAE5C,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,SAAS,mBAAmB;gBAAE,QAAQ;YAAI;QACvD;QAEA,2BAA2B;QAC3B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,QAAQ;gBACzC,QAAQ,SAAS,MAAM;gBACvB,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO;YACxC,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;YACjC,OAAO;YACP,SAAS,MAAM,OAAO;QACxB,IAAI;YACF,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF", "debugId": null}}]}