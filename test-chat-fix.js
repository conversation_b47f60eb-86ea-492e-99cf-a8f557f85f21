// Test script to verify the chat API is working correctly
async function testChatAPI() {
  try {
    console.log('🧪 Testing chat API...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        message: 'Hello! This is a test message.', 
        threadId: null 
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ API Response:', data);
    
    // Verify response structure
    if (data.response && data.threadId && data.status === 'success') {
      console.log('✅ Response structure is correct');
      console.log('📝 AI Response:', data.response);
      console.log('🔗 Thread ID:', data.threadId);
    } else {
      console.log('❌ Response structure is incorrect');
      console.log('Expected: { response, threadId, status }');
      console.log('Received:', Object.keys(data));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testChatAPI();
