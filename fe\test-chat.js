// Simple test to verify chat functionality
async function testChat() {
  try {
    console.log('Testing chat API...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        message: 'Hello! Can you tell me about AI?', 
        threadId: null 
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ API responded successfully');
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const reader = response.body.getReader();
    let threadId = null;
    let messageContent = '';
    let eventCount = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const lines = new TextDecoder().decode(value).split('\n').filter(Boolean);
      
      for (const line of lines) {
        try {
          const event = JSON.parse(line);
          eventCount++;
          
          console.log(`Event ${eventCount}: ${event.event}`);
          
          if (event.event === 'thread.created') {
            threadId = event.data.id;
            console.log(`  Thread ID: ${threadId}`);
          }
          
          if (event.event === 'thread.message.created') {
            const message = event.data;
            if (message.role === 'assistant' && message.content) {
              const textContent = message.content.find(c => c.type === 'text');
              if (textContent && textContent.text) {
                messageContent = textContent.text.value;
                console.log(`  Assistant response: ${messageContent.substring(0, 100)}...`);
              }
            }
          }
          
          if (event.event === 'thread.run.completed') {
            console.log('  Run completed successfully');
          }
          
        } catch (e) {
          console.log(`  Failed to parse line: ${line.substring(0, 50)}...`);
        }
      }
    }
    
    console.log(`\n✅ Chat test completed successfully!`);
    console.log(`📊 Total events received: ${eventCount}`);
    console.log(`🆔 Thread ID: ${threadId}`);
    console.log(`💬 Assistant response: ${messageContent}`);
    
  } catch (error) {
    console.error('❌ Chat test failed:', error.message);
  }
}

testChat();
