const express = require('express');
require('dotenv').config();

const corsMiddleware = require('./middleware/cors');
const errorHandler = require('./middleware/errorHandler');
const chatRoutes = require('./routes/chatRoutes');
const assistantRoutes = require('./routes/assistantRoutes');

const app = express();

// Middleware
app.use(corsMiddleware);
app.use(express.json());

// Routes
app.use('/', chatRoutes);
app.use('/', assistantRoutes);

// Error handling middleware
app.use(errorHandler);

module.exports = app;