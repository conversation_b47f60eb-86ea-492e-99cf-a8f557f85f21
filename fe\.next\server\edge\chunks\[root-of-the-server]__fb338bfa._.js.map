{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\r\nimport { OpenAI } from 'openai';\r\n\r\nexport const runtime = 'edge';\r\n\r\nconst openai = new OpenAI({\r\n  apiKey: process.env.OPENAI_API_KEY,\r\n});\r\n\r\nexport async function POST(req: NextRequest) {\r\n  const { threadId, message } = await req.json();\r\n  const assistantId = process.env.ASSISTANT_ID;\r\n  if (!assistantId) {\r\n    return new Response('Missing ASSISTANT_ID', { status: 500 });\r\n  }\r\n  if (!message) {\r\n    return new Response('Missing message', { status: 400 });\r\n  }\r\n\r\n  // 1. Buat thread jika belum ada\r\n  let thread_id = threadId;\r\n  let newThread = null;\r\n  if (!thread_id) {\r\n    newThread = await openai.beta.threads.create();\r\n    thread_id = newThread.id;\r\n  }\r\n\r\n  // 2. Tambahkan pesan user ke thread\r\n  await openai.beta.threads.messages.create(thread_id, {\r\n    role: 'user',\r\n    content: message,\r\n  });\r\n\r\n  // 3. Jalankan assistant (run)\r\n  const run = await openai.beta.threads.runs.create(thread_id, {\r\n    assistant_id: assistantId,\r\n    stream: true,\r\n  });\r\n\r\n  // 4. Stream event ke client\r\n  const encoder = new TextEncoder();\r\n  const stream = new ReadableStream({\r\n    async start(controller) {\r\n      // Kirim event thread.created jika thread baru dibuat\r\n      if (newThread) {\r\n        controller.enqueue(encoder.encode(JSON.stringify({\r\n          event: 'thread.created',\r\n          data: { id: newThread.id }\r\n        }) + '\\n'));\r\n      }\r\n\r\n      for await (const event of run) {\r\n        controller.enqueue(encoder.encode(JSON.stringify(event) + '\\n'));\r\n        if (event.event === 'thread.run.completed') break;\r\n      }\r\n      controller.close();\r\n    },\r\n  });\r\n\r\n  return new Response(stream, {\r\n    headers: {\r\n      'Content-Type': 'text/event-stream',\r\n      'Cache-Control': 'no-cache',\r\n    },\r\n  });\r\n} "], "names": [], "mappings": ";;;;AACA;AAAA;;AAEO,MAAM,UAAU;AAEvB,MAAM,SAAS,IAAI,2IAAA,CAAA,SAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,eAAe,KAAK,GAAgB;IACzC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,IAAI;IAC5C,MAAM,cAAc,QAAQ,GAAG,CAAC,YAAY;IAC5C,IAAI,CAAC,aAAa;QAChB,OAAO,IAAI,SAAS,wBAAwB;YAAE,QAAQ;QAAI;IAC5D;IACA,IAAI,CAAC,SAAS;QACZ,OAAO,IAAI,SAAS,mBAAmB;YAAE,QAAQ;QAAI;IACvD;IAEA,gCAAgC;IAChC,IAAI,YAAY;IAChB,IAAI,YAAY;IAChB,IAAI,CAAC,WAAW;QACd,YAAY,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC5C,YAAY,UAAU,EAAE;IAC1B;IAEA,oCAAoC;IACpC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW;QACnD,MAAM;QACN,SAAS;IACX;IAEA,8BAA8B;IAC9B,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;QAC3D,cAAc;QACd,QAAQ;IACV;IAEA,4BAA4B;IAC5B,MAAM,UAAU,IAAI;IACpB,MAAM,SAAS,IAAI,eAAe;QAChC,MAAM,OAAM,UAAU;YACpB,qDAAqD;YACrD,IAAI,WAAW;gBACb,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC,KAAK,SAAS,CAAC;oBAC/C,OAAO;oBACP,MAAM;wBAAE,IAAI,UAAU,EAAE;oBAAC;gBAC3B,KAAK;YACP;YAEA,WAAW,MAAM,SAAS,IAAK;gBAC7B,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC,KAAK,SAAS,CAAC,SAAS;gBAC1D,IAAI,MAAM,KAAK,KAAK,wBAAwB;YAC9C;YACA,WAAW,KAAK;QAClB;IACF;IAEA,OAAO,IAAI,SAAS,QAAQ;QAC1B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF"}}]}