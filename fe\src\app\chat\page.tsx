"use client";
import { useState, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Send } from "lucide-react";

interface Message {
  role: "user" | "assistant";
  content: string;
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [streaming, setStreaming] = useState(false);
  const [threadId, setThreadId] = useState<string | null>(null);
  const chatRef = useRef<HTMLDivElement>(null);

  const sendMessage = useMutation({
    mutationFn: async ({ message, threadId: tId }: { message: string; threadId: string | null }) => {
      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message, threadId: tId }),
      });
      if (!res.body) throw new Error("No stream");
      const reader = res.body.getReader();
      let aiMsg = "";
      setStreaming(true);
      let newThreadId = tId;
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const lines = new TextDecoder().decode(value).split("\n").filter(Boolean);
        for (const line of lines) {
          try {
            const event = JSON.parse(line);
            if (event.event === "thread.created") {
              newThreadId = event.data.id;
              setThreadId(newThreadId);
            }
            if (event.event === "thread.run.step.delta") {
              // delta content
              const delta = event.data.delta;
              if (delta && delta.step_details && delta.step_details.message_creation) {
                // Handle message creation delta
                const messageCreation = delta.step_details.message_creation;
                if (messageCreation.message && messageCreation.message.content) {
                  const content = messageCreation.message.content[0];
                  if (content && content.text && content.text.value) {
                    aiMsg = content.text.value;
                    setMessages((msgs) => {
                      const last = msgs[msgs.length - 1];
                      if (last && last.role === "assistant") {
                        return [...msgs.slice(0, -1), { ...last, content: aiMsg }];
                      }
                      return [...msgs, { role: "assistant", content: aiMsg }];
                    });
                  }
                }
              }
            }
            if (event.event === "thread.message.delta") {
              // Handle message delta for streaming text
              const delta = event.data.delta;
              if (delta && delta.content) {
                for (const contentPart of delta.content) {
                  if (contentPart.type === "text" && contentPart.text) {
                    aiMsg += contentPart.text.value || "";
                    setMessages((msgs) => {
                      const last = msgs[msgs.length - 1];
                      if (last && last.role === "assistant") {
                        return [...msgs.slice(0, -1), { ...last, content: aiMsg }];
                      }
                      return [...msgs, { role: "assistant", content: aiMsg }];
                    });
                  }
                }
              }
            }
            if (event.event === "thread.message.created") {
              // Handle complete message (non-streaming)
              const message = event.data;
              if (message && message.role === "assistant" && message.content) {
                const textContent = message.content.find((c: any) => c.type === "text");
                if (textContent && textContent.text) {
                  aiMsg = textContent.text.value;
                  setMessages((msgs) => {
                    const last = msgs[msgs.length - 1];
                    if (last && last.role === "assistant") {
                      return [...msgs.slice(0, -1), { ...last, content: aiMsg }];
                    }
                    return [...msgs, { role: "assistant", content: aiMsg }];
                  });
                }
              }
            }
            if (event.event === "thread.run.completed") {
              setThreadId(newThreadId);
            }
          } catch {}
        }
      }
      setStreaming(false);
    },
  });

  const handleSend = () => {
    if (!input.trim() || streaming) return;
    setMessages((msgs) => [...msgs, { role: "user", content: input }]);
    sendMessage.mutate({ message: input, threadId });
    setInput("");
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
    }, 100);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-xl p-4 flex flex-col gap-4">
        <div ref={chatRef} className="flex flex-col gap-3 h-[60vh] overflow-y-auto border-b pb-4">
          {messages.length === 0 && (
            <div className="text-muted-foreground text-center mt-10">Mulai chat dengan AI Assistant!</div>
          )}
          {messages.map((msg, i) => (
            <div key={i} className={`flex gap-2 items-start ${msg.role === "user" ? "justify-end" : "justify-start"}`}>
              {msg.role === "assistant" && (
                <Avatar className="bg-muted">
                  <img src="/ai.svg" alt="AI" className="w-6 h-6 rounded-full" />
                </Avatar>
              )}
              <div className={`rounded-lg px-3 py-2 max-w-[70%] text-sm ${msg.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                {msg.content}
              </div>
              {msg.role === "user" && (
                <Avatar className="bg-primary">
                  <img src="/user.svg" alt="User" className="w-6 h-6 rounded-full" />
                </Avatar>
              )}
            </div>
          ))}
        </div>
        <form className="flex gap-2" onSubmit={e => { e.preventDefault(); handleSend(); }}>
          <Input
            value={input}
            onChange={e => setInput(e.target.value)}
            placeholder="Ketik pesan..."
            disabled={streaming}
            className="flex-1"
            autoFocus
            onKeyDown={e => { if (e.key === "Enter" && !e.shiftKey) { e.preventDefault(); handleSend(); } }}
          />
          <Button type="submit" disabled={streaming || !input.trim()} size="icon">
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </Card>
    </div>
  );
} 