"use client";
import { useState, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Send, Loader2 } from "lucide-react";

interface Message {
  role: "user" | "assistant";
  content: string;
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [threadId, setThreadId] = useState<string | null>(null);
  const chatRef = useRef<HTMLDivElement>(null);

  const sendMessage = useMutation({
    mutationFn: async ({ message, threadId: tId }: { message: string; threadId: string | null }) => {
      setIsLoading(true);

      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message, threadId: tId }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();

      // Update thread ID if we got a new one
      if (data.threadId) {
        setThreadId(data.threadId);
      }

      // Add the AI response to messages
      if (data.response) {
        setMessages((msgs) => [...msgs, { role: "assistant", content: data.response }]);
      }

      setIsLoading(false);
      return data;
    },
    onError: () => {
      setIsLoading(false);
    }
  });

  const handleSend = () => {
    if (!input.trim() || isLoading) return;
    setMessages((msgs) => [...msgs, { role: "user", content: input }]);
    sendMessage.mutate({ message: input, threadId });
    setInput("");
    setTimeout(() => {
      chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight, behavior: "smooth" });
    }, 100);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-xl p-4 flex flex-col gap-4">
        <div ref={chatRef} className="flex flex-col gap-3 h-[60vh] overflow-y-auto border-b pb-4">
          {messages.length === 0 && (
            <div className="text-muted-foreground text-center mt-10">Mulai chat dengan AI Assistant!</div>
          )}
          {messages.map((msg, i) => (
            <div key={i} className={`flex gap-2 items-start ${msg.role === "user" ? "justify-end" : "justify-start"}`}>
              {msg.role === "assistant" && (
                <Avatar className="bg-muted">
                  <img src="/ai.svg" alt="AI" className="w-6 h-6 rounded-full" />
                </Avatar>
              )}
              <div className={`rounded-lg px-3 py-2 max-w-[70%] text-sm ${msg.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                {msg.content}
              </div>
              {msg.role === "user" && (
                <Avatar className="bg-primary">
                  <img src="/user.svg" alt="User" className="w-6 h-6 rounded-full" />
                </Avatar>
              )}
            </div>
          ))}
          {isLoading && (
            <div className="flex gap-2 items-start justify-start">
              <Avatar className="bg-muted">
                <img src="/ai.svg" alt="AI" className="w-6 h-6 rounded-full" />
              </Avatar>
              <div className="rounded-lg px-3 py-2 max-w-[70%] text-sm bg-muted flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-muted-foreground">AI sedang mengetik...</span>
              </div>
            </div>
          )}
        </div>
        <form className="flex gap-2" onSubmit={e => { e.preventDefault(); handleSend(); }}>
          <Input
            value={input}
            onChange={e => setInput(e.target.value)}
            placeholder="Ketik pesan..."
            disabled={isLoading}
            className="flex-1"
            autoFocus
            onKeyDown={e => { if (e.key === "Enter" && !e.shiftKey) { e.preventDefault(); handleSend(); } }}
          />
          <Button type="submit" disabled={isLoading || !input.trim()} size="icon">
            {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
          </Button>
        </form>
      </Card>
    </div>
  );
} 