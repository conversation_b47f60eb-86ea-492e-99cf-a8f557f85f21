const openai = require('../config/openai');
const fs = require('fs');

class AssistantService {
  async uploadFile(filePath, fileName) {
    try {
      const file = await openai.files.create({
        file: fs.createReadStream(filePath),
        purpose: 'assistants'
      });
      
      return file.id;
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  async createAssistant(assistantData, fileId = null) {
    try {
      const assistantConfig = {
        name: assistantData.name,
        instructions: assistantData.instructions,
        model: assistantData.model || 'gpt-4-turbo-preview',
        tools: []
      };

      // Add retrieval tool if file is provided
      if (fileId) {
        assistantConfig.tools.push({ type: 'retrieval' });
        assistantConfig.file_ids = [fileId];
      }

      const assistant = await openai.beta.assistants.create(assistantConfig);
      
      return {
        id: assistant.id,
        name: assistant.name,
        instructions: assistant.instructions,
        model: assistant.model,
        tools: assistant.tools,
        file_ids: assistant.file_ids || []
      };
    } catch (error) {
      console.error('Assistant creation error:', error);
      throw new Error(`Failed to create assistant: ${error.message}`);
    }
  }

  async processAssistantCreation(assistantData, file = null) {
    try {
      let fileId = null;

      // Upload file if provided
      if (file) {
        fileId = await this.uploadFile(file.path, file.originalname);
        
        // Clean up temporary file
        fs.unlinkSync(file.path);
      }

      // Create assistant
      const assistant = await this.createAssistant(assistantData, fileId);

      return {
        success: true,
        assistant
      };
    } catch (error) {
      // Clean up temporary file if it exists
      if (file && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  }

  validateAssistantData(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.instructions || data.instructions.trim() === '') {
      errors.push('Instructions are required');
    }

    if (data.model && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    return errors;
  }

  isValidModel(model) {
    const validModels = [
      'gpt-4-turbo-preview',
      'gpt-4',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k'
    ];
    return validModels.includes(model);
  }

  isValidFileType(mimetype, filename) {
    const validMimeTypes = [
      'application/pdf',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/markdown',
      'application/json'
    ];

    const validExtensions = ['.pdf', '.txt', '.docx', '.doc', '.md', '.json'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));

    return validMimeTypes.includes(mimetype) || validExtensions.includes(fileExtension);
  }
}

module.exports = new AssistantService();