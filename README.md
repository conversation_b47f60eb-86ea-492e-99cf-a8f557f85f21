# AI Trainer Hub

Aplikasi chat AI dengan arsitektur terpisah antara frontend dan backend.

## Struktur Proyek

```
├── be/                          # Backend (Express.js + OpenAI API)
│   ├── src/
│   │   ├── config/
│   │   │   └── openai.js       # OpenAI configuration
│   │   ├── controllers/
│   │   │   └── chatController.js # Request handlers
│   │   ├── services/
│   │   │   └── chatService.js   # Business logic
│   │   ├── routes/
│   │   │   └── chatRoutes.js    # API routes
│   │   ├── middleware/
│   │   │   ├── cors.js          # CORS configuration
│   │   │   └── errorHandler.js  # Error handling
│   │   └── app.js               # Express app setup
│   ├── server.js                # Server entry point
│   ├── package.json
│   └── .env
├── fe/                          # Frontend (Next.js)
└── README.md
```

## Setup dan Instalasi

### 1. Backend Setup

```bash
cd be
npm install
```

Pastikan file `.env` sudah ada dengan konfigurasi:
```
OPENAI_API_KEY=your_openai_api_key
ASSISTANT_ID=your_assistant_id
PORT=3001
FRONTEND_URL=http://localhost:3000
```

### 2. Frontend Setup

```bash
cd fe
npm install
```

Pastikan file `.env.local` sudah ada dengan konfigurasi:
```
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## Menjalankan Aplikasi

### 1. Jalankan Backend (Terminal 1)
```bash
cd be
npm run dev
```
Backend akan berjalan di `http://localhost:3001`

### 2. Jalankan Frontend (Terminal 2)
```bash
cd fe
npm run dev
```
Frontend akan berjalan di `http://localhost:3000`

## Arsitektur

- **Frontend (fe/)**: Next.js app yang menangani UI dan user interaction
- **Backend (be/)**: Express.js server yang bertindak sebagai proxy ke OpenAI API
- **Security**: API key dan assistant ID disimpan di backend, tidak terekspos ke frontend

## API Endpoints

### Backend Endpoints:
- `GET /health` - Health check
- `POST /api/chat` - Chat dengan AI assistant
- `GET /api/thread/:threadId/messages` - Ambil pesan dari thread
- `POST /api/assistants` - Buat assistant baru (dengan optional file upload)
- `GET /api/assistants` - List semua assistants
- `GET /api/assistants/:assistantId` - Get detail assistant

### Frontend API Routes:
- `POST /api/chat` - Proxy ke backend chat endpoint

## Development

Untuk development, jalankan kedua server secara bersamaan:
1. Backend di port 3001
2. Frontend di port 3000

Frontend akan otomatis proxy request ke backend melalui API routes.

## Assistant API

### Create Assistant
**POST** `/api/assistants`

Create a new OpenAI assistant with optional file upload for knowledge base.

**Request (multipart/form-data):**
```
name: "Assistant Name" (required)
instructions: "System instructions" (required)  
model: "gpt-4-turbo-preview" (optional, default: gpt-4-turbo-preview)
file: [file upload] (optional)
```

**Supported File Types:**
- PDF (.pdf)
- Text (.txt)
- Word Documents (.docx, .doc)
- Markdown (.md)
- JSON (.json)

**Response:**
```json
{
  "success": true,
  "assistant": {
    "id": "asst_...",
    "name": "Assistant Name",
    "instructions": "...",
    "model": "gpt-4-turbo-preview",
    "tools": [...],
    "file_ids": [...]
  }
}
```

### List Assistants
**GET** `/api/assistants`

Get list of all created assistants.

### Get Assistant
**GET** `/api/assistants/:assistantId`

Get details of a specific assistant.