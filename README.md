# AI Trainer Hub

Aplikasi chat AI dengan arsitektur terpisah antara frontend dan backend.

## Struktur Proyek

```
├── be/          # Backend (Express.js + OpenAI API)
├── fe/          # Frontend (Next.js)
└── README.md
```

## Setup dan Instalasi

### 1. Backend Setup

```bash
cd be
npm install
```

Pastikan file `.env` sudah ada dengan konfigurasi:
```
OPENAI_API_KEY=your_openai_api_key
ASSISTANT_ID=your_assistant_id
PORT=3001
FRONTEND_URL=http://localhost:3000
```

### 2. Frontend Setup

```bash
cd fe
npm install
```

Pastikan file `.env.local` sudah ada dengan konfigurasi:
```
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## Menjalankan Aplikasi

### 1. Jalankan Backend (Terminal 1)
```bash
cd be
npm run dev
```
Backend akan berjalan di `http://localhost:3001`

### 2. Jalankan Frontend (Terminal 2)
```bash
cd fe
npm run dev
```
Frontend akan ber<PERSON>lan di `http://localhost:3000`

## Arsitektur

- **Frontend (fe/)**: Next.js app yang menangani UI dan user interaction
- **Backend (be/)**: Express.js server yang bertindak sebagai proxy ke OpenAI API
- **Security**: API key dan assistant ID disimpan di backend, tidak terekspos ke frontend

## API Endpoints

### Backend Endpoints:
- `GET /health` - Health check
- `POST /api/chat` - Chat dengan AI assistant
- `GET /api/thread/:threadId/messages` - Ambil pesan dari thread

### Frontend API Routes:
- `POST /api/chat` - Proxy ke backend chat endpoint

## Development

Untuk development, jalankan kedua server secara bersamaan:
1. Backend di port 3001
2. Frontend di port 3000

Frontend akan otomatis proxy request ke backend melalui API routes.