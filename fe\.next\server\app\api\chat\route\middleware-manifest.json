{"sorted_middleware": ["/api/chat/route"], "middleware": {}, "instrumentation": null, "functions": {"/api/chat/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/api/chat/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_api_chat_route_actions_cf3bea7b.js", "server/edge/chunks/_next-internal_server_app_api_chat_route_actions_9151dbe9.js", "server/edge/chunks/node_modules_next_dist_esm_bea47e53._.js", "server/edge/chunks/node_modules_next_dist_compiled_f15f3a88._.js", "server/edge/chunks/node_modules_next_dist_48e46017._.js", "server/edge/chunks/edge-wrapper_307bef14.js", "server/edge/chunks/node_modules_openai_6a6234b1._.js", "server/edge/chunks/[root-of-the-server]__fb338bfa._.js", "server/edge/chunks/edge-wrapper_0098082e.js", "server/app/api/chat/route/react-loadable-manifest.js"], "name": "/api/chat", "page": "/api/chat/route", "matchers": [{"regexp": "^/api/chat(?:/)?$", "originalSource": "/api/chat"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6xk0DfgzIOdSE+P7u5qNZ58E4XWG/R/iv32/ABFh1Rc=", "__NEXT_PREVIEW_MODE_ID": "c0dc8c27973f40705856563e3d4b3689", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fc667d6996c8709c29f135abdf206e2e63309ae81c704e9b6f38e815f2eccb10", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "12d92519100e6a4b49c82905343a2f7cba9097bb11db1a574c7089a504f46ea6"}}}}