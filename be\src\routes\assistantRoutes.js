const express = require('express');
const assistantController = require('../controllers/assistantController');
const { upload, handleUploadError } = require('../middleware/upload');

const router = express.Router();

// Create assistant endpoint with optional file upload
router.post('/api/assistants', upload, handleUploadError, assistantController.createAssistant);

// List all assistants
router.get('/api/assistants', assistantController.listAssistants);

// Get specific assistant
router.get('/api/assistants/:assistantId', assistantController.getAssistant);

module.exports = router;